import RPi.GPIO as GPIO
import time

# 设置BCM引脚编号模式
GPIO.setmode(GPIO.BCM)

# 定义LED引脚（建议使用标准GPIO引脚）
led_pins = [18, 15, 14, 23, 24, 25]  # 替换1,7为23,24避免与I2C/SPI冲突

# 初始化引脚
for pin in led_pins:
    GPIO.setup(pin, GPIO.OUT)
    GPIO.output(pin, GPIO.LOW)  # 初始关闭

try:
    print("LED交替闪烁中，按 Ctrl+C 停止")
    while True:
        # 依次点亮每个LED
        for i, pin in enumerate(led_pins):
            print(f"点亮第{i+1}个LED (GPIO {pin})")
            GPIO.output(pin, GPIO.HIGH)
            time.sleep(0.5)
            GPIO.output(pin, GPIO.LOW)

except KeyboardInterrupt:
    print("\n程序终止")
finally:
    GPIO.cleanup()
